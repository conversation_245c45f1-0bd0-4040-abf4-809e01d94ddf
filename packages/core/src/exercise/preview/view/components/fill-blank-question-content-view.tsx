import { FormatMath } from "@repo/core/exercise/components";
import HalfRightIconRed from "@repo/core/public/assets/stu-exercise/icons/fill-blank/half-right-red.svg";
import RightIconGreen from "@repo/core/public/assets/stu-exercise/icons/fill-blank/right-green.svg";
import WrongIconRed from "@repo/core/public/assets/stu-exercise/icons/fill-blank/wrong-red.svg";
import { cn } from "@repo/ui/lib/utils";
import { FC, useRef } from "react";
import { NextQuestionInfo } from "../../../model";
import { useQuestionPreviewContext } from "../../contexts/question-preview-context";
import {
  FillBlankViewModel,
} from "../../viewmodel/fill-blank-question-viewmodel";
import { parseContent } from "../../../utils/question/parseFillBlank";
import { UserAnswerData } from "@repo/core/exercise/model/types";

interface QuestionContentProps {
  answers: { id: number; value: string }[];
  activeBlankIndex: number;
  onBlankClick: (index: number) => void;
}

/**
 * 填空题内容渲染组件
 *
 * ✅ 已适配新的统一架构：
 * - 移除对已删除的 useExerciseSessionState 的依赖
 * - 通过统一的 question-context 和 question-viewmodel 获取数据
 * - 添加语义化 class 名便于调试
 *
 * 职责：
 * - 解析并渲染填空题的题目内容
 * - 渲染可点击的空白填入区域
 * - 显示答案状态和自评结果
 */
const FillBlankQuestionContent = ({
  answers,
  activeBlankIndex,
  onBlankClick,
  isReview = false,
  blankRefs,
  question,
  selfEvaluation,
  inputMode,
}: QuestionContentProps & {
  correctAnswers?: string[];
  isReview?: boolean;
  blankRefs?: React.MutableRefObject<(HTMLSpanElement | null)[]>;
  question: NextQuestionInfo;
  selfEvaluation?: ("right" | "partial" | "wrong" | null)[];
  inputMode: UserAnswerData["inputMode"];
}) => {
  let blankIdx = 0;

  const paragraphs = parseContent(question.questionContent.questionStem || "");
  // 计算总空数
  const totalBlanks = answers.length;
  // 是否只有一个空
  const hasSingleBlank = totalBlanks === 1;
  return (
    <div className="fill_blank_question_content_container h-full w-full">
      <div className="fill_blank_content_wrapper flex flex-col gap-4">
        <div className="fill_blank_text_content text-base leading-8">
          {paragraphs.map((parts, pIdx) => (
            <div
              key={pIdx}
              className="fill_blank_paragraph mb-5 leading-[190%]"
            >
              {parts.map((part, idx) => {
                if (part === null) {
                  const i = blankIdx;
                  blankIdx++;
                  let evalResult: "right" | "partial" | "wrong" | null = null;
                  if (isReview && selfEvaluation && selfEvaluation[i]) {
                    evalResult = selfEvaluation[i]!;
                  }
                  let colorClass = "text-[#0A8AC2]";
                  let borderClass = "border-1";
                  let bgClass = "";
                  let icon = null;
                  if (isReview && evalResult) {
                    if (evalResult === "right") {
                      borderClass = "border-b-[#84D64B]";
                      colorClass = "text-[#449908] border-[#84D64B]";
                      icon = (
                        <RightIconGreen className="ml-1 inline-block h-4 w-4 align-middle" />
                      );
                    } else if (evalResult === "partial") {
                      borderClass = "border-b-[#FFD466]";
                      colorClass = "text-[#CC6204] border-[#FFD466]";
                      icon = (
                        <HalfRightIconRed className="ml-1 inline-block h-4 w-4 align-middle" />
                      );
                    } else if (evalResult === "wrong") {
                      borderClass = "border-b-[#FF6139]";
                      colorClass = "text-[#D1320A] border-[#FF6139]";
                      icon = (
                        <WrongIconRed className="ml-1 inline-block h-4 w-4 align-middle" />
                      );
                    }
                    if (activeBlankIndex === i) {
                      if (evalResult === "right") {
                        bgClass = "bg-[rgba(132,214,75,0.3)]";
                      } else if (evalResult === "partial") {
                        bgClass = "bg-[rgba(255,212,102,0.3)]";
                      } else if (evalResult === "wrong") {
                        bgClass = "bg-[rgba(255,123,89,0.3)]";
                      }
                    }
                  } else {
                    // 未进入解析状态，选中高亮
                    if (activeBlankIndex === i) {
                      borderClass = "border-b-[#58C4FA]";
                      bgClass = "";
                      colorClass = "text-[#0A8AC2]";
                    } else {
                      borderClass = "border-b-1";
                      bgClass = "";
                      colorClass = "text-[#0A8AC2]";
                    }
                  }
                  // 字数过多深色
                  const isLong = (answers[i]?.value || "").length > 6;
                  const longClass = isLong ? "" : "";
                  const isValueClass = answers[i]?.value ? "ml-3" : "";
                  return (
                    <span
                      key={"blank-" + i}
                      ref={(el) => {
                        if (blankRefs) blankRefs.current[i] = el;
                      }}
                      className={`fill_blank_input_area border-b-1 relative mx-1 inline-flex h-8 w-[10.625rem] cursor-pointer items-center justify-center align-top ${borderClass} ${colorClass} ${bgClass || ""}`}
                      // onClick={() => onBlankClick(i)}
                    >
                      {!hasSingleBlank && (<span
                        className={cn(
                          "fill_blank_number_indicator border-1 flex h-5 w-5 flex-shrink-0 cursor-pointer items-center justify-center rounded-full border-solid text-center text-xs",
                          isValueClass,
                          isReview && evalResult
                            ? activeBlankIndex === i && evalResult === "right"
                              ? "border-[#84D64B] bg-[#84D64B] text-white"
                              : activeBlankIndex === i &&
                                  evalResult === "partial"
                                ? "border-[#FFD466] bg-[#FFD466] text-white"
                                : activeBlankIndex === i &&
                                    evalResult === "wrong"
                                  ? "border-[#FF6139] bg-[#FF6139] text-white"
                                  : colorClass
                            : activeBlankIndex === i
                              ? answers[i]?.value
                                ? "border-[#58C4FA] bg-[#58C4FA] text-white"
                                : "border-[#58C4FA] bg-[#58C4FA] text-white"
                              : colorClass
                        )}
                        >
                          {i + 1}
                        </span>
                      )}
                      {answers[i]?.value && inputMode === "keyboard" && (
                        <span
                          className={`fill_blank_answer_text ${hasSingleBlank ? "" : "ml-2"} flex-1 truncate ${colorClass} ${longClass}`}
                        >
                          {answers[i].value}
                        </span>
                      )}
                      {isReview && evalResult && (
                        <span className="fill_blank_evaluation_icon mr-3 flex items-center">
                          {icon}
                        </span>
                      )}
                    </span>
                  );
                }
                return (
                  <FormatMath
                    key={"part-" + idx}
                    htmlContent={part}
                    questionId={question.questionId}
                  />
                );
              })}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

/**
 * 填空题内容视图组件
 *
 * ✅ 已适配新的统一架构：
 * - 只需要调用 useQuestionPreviewContext 获取题目数据
 * - 移除重复的ViewModel调用，简化数据获取
 *
 * 职责：
 * - 获取题目数据并传递给内容渲染组件
 * - 管理空白区域的引用和滚动行为
 */
export const FillBlankQuestionContentView: FC<{
  viewModel?: FillBlankViewModel;
}> = ({ viewModel }) => {
  // 🎯 只需要从统一Context获取题目数据
  const { currentQuestion: question } = useQuestionPreviewContext();

  // Hooks 必须在顶层调用
  const blankRefs = useRef<(HTMLSpanElement | null)[]>([]);

  // 安全地获取 activeBlankIndex，如果 viewModel 不存在则使用默认值
  const activeBlankIndex = viewModel?.activeBlankIndex ?? 0;

  if (!question) {
    return <div className="fill_blank_content_loading">加载中...</div>;
  }

  // 如果没有传入 viewModel，显示占位符
  if (!viewModel) {
    return (
      <div className="fill_blank_content_placeholder py-8 text-center">
        <div className="text-gray-500">填空题 ViewModel 未初始化</div>
      </div>
    );
  }

  const {
    answers,
    handleBlankClick,
    isReview,
    correctAnswers,
    selfEvaluation,
    inputMode,
  } = viewModel;

  return (
    <FillBlankQuestionContent
      question={question}
      answers={answers}
      activeBlankIndex={activeBlankIndex}
      onBlankClick={handleBlankClick}
      correctAnswers={correctAnswers}
      isReview={isReview}
      blankRefs={blankRefs}
      selfEvaluation={selfEvaluation}
      inputMode={inputMode}
    />
  );
};
