import { toast } from "@repo/core/components/stu-toast";
import { FormatMath } from "@repo/core/exercise/components";
import HalfRightIconNormal from "@repo/core/public/assets/stu-exercise/icons/fill-blank/half-right-normal.svg";
import HalfRightIconRed from "@repo/core/public/assets/stu-exercise/icons/fill-blank/half-right-red.svg";
import RightIconGreen from "@repo/core/public/assets/stu-exercise/icons/fill-blank/right-green.svg";
import RightIconNormal from "@repo/core/public/assets/stu-exercise/icons/fill-blank/right-normal.svg";
import WrongIconNormal from "@repo/core/public/assets/stu-exercise/icons/fill-blank/wrong-normal.svg";
import WrongIconRed from "@repo/core/public/assets/stu-exercise/icons/fill-blank/wrong-red.svg";
import { cn } from "@repo/ui/lib/utils";
import { Camera, Keyboard } from "lucide-react";
import React, { useEffect } from "react";
import { useQuestionPreviewContext } from "../../contexts/question-preview-context";
import { FillBlankViewModel } from "../../viewmodel/fill-blank-question-viewmodel";
import CameraInput from "./camera-view";
import KeyboardInput from "./keyboard-input-view";

export const AnswerAreaView: React.FC<{
  type: "fill-blank" | "solution" | "english-fill-blank";
  viewModel: FillBlankViewModel;
}> = ({ type, viewModel }) => {
  // 🎯 只需要从统一Context获取题目数据
  const {
    currentQuestion: question,
    updateUserAnswer,
    previewData,
  } = useQuestionPreviewContext();

  // 使用填空题专用的 viewModel
  const fillBlankViewModel = viewModel;

  const {
    answers,
    activeBlankIndex,
    inputMode,
    imageFiles,
    handleModeChange,
    handleRemoveImage,
    handleRetryUpload,
    correctAnswers,
    selfEvaluation,
    isReview,
  } = fillBlankViewModel;

  // 🔧 新增：预览模式下根据每个空的 evaluationResult 设置自评按钮高亮
  useEffect(() => {
    if (previewData?.studentAnswer?.answerContents) {
      const answerContents = previewData.studentAnswer.answerContents;
      // 设置答案
      const answers = answerContents.map((item, index) => ({
        id: index,
        value: item.content[0] || "",
      }));
      fillBlankViewModel.setAnswers(answers);
      // 为每个空设置独立的自评结果
      const newSelfEvaluation = answerContents.map((_) => {
        // evaluationResult是数组，每个元素对应一个空的自评结果
        const answerResult = _.answerResult;
        if (answerResult !== undefined) {
          // 根据每个空的 evaluationResult 映射到自评结果
          switch (answerResult) {
            case 1: // 正确
              return "right";
            case 2: // 错误
              return "wrong";
            case 3: // 部分正确
              return "partial";
            default:
              return 'wrong';
          }
        }
        return 'wrong'; // 显式返回 null，避免 undefined
      });

      // 🔧 只有当新的自评结果与当前不同时才更新，避免无限循环
      const currentSelfEvaluation = fillBlankViewModel.selfEvaluation;
      const hasChanged = newSelfEvaluation.some(
        (newVal, index) => newVal !== currentSelfEvaluation[index]
      );

      if (hasChanged) {
        fillBlankViewModel.setSelfEvaluation(newSelfEvaluation);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [previewData?.studentAnswer?.answerContents, answers.length]);

  // 将答案同步到 QuestionContext 的 userAnswerData
  useEffect(() => {
    // 🔧 预览模式下不需要同步答案数据
    return;

    // // eslint-disable-next-line no-unreachable
    // if (inputMode === "camera") {
    //   updateUserAnswer({
    //     inputMode: "camera",
    //     imgFiles: imageFiles.map(img => img.file),
    //   });
    // } else {
    //   if (type === "solution") {
    //     // 解答题：将第一个答案作为 subjectiveAnswer
    //     const subjectiveAnswer = fillBlankViewModel.answers[0]?.value || "";
    //     updateUserAnswer({
    //       inputMode: "keyboard",
    //       subjectiveAnswer,
    //     });
    //   } else if (type === "fill-blank") {
    //     // 🔧 修复：填空题使用 subjectiveAnswer 数组
    //     const subjectiveAnswer = fillBlankViewModel.answers.map(
    //       (answer) => answer.value
    //     );

    //     updateUserAnswer({
    //       inputMode: "keyboard",
    //       subjectiveAnswer,
    //     });
    //   } else if (type === "english-fill-blank") {
    //     updateUserAnswer({
    //       englishFillBlankAnswers: fillBlankViewModel.answers.map(
    //         (answer) => answer.value
    //       ),
    //     });
    //   }
    // }
  }, [
    fillBlankViewModel.answers,
    inputMode,
    imageFiles,
    updateUserAnswer,
    type,
  ]);

  return (
    <div
      className={cn(
        "flex-1 overflow-y-auto overflow-x-hidden",
        !isReview ? "h-full" : "h-auto"
      )}
    >
      <div className="flex h-full flex-col">
        {/* 输入模式切换 */}
        <div className="mt-2 flex items-center justify-center gap-2">
          {!isReview && (
            <div className="flex-1 justify-start font-['Resource_Han_Rounded_SC'] text-base font-normal leading-relaxed text-zinc-800/70">
              请认真作答，老师会检查作答结果
            </div>
          )}
          {!isReview && type !== "english-fill-blank" && (
            <>
              <div className="relative flex h-8 w-[180px] items-center gap-2 rounded-lg bg-stone-900/5 p-1">
                {/* 滑块动画 */}
                <div
                  className="absolute bottom-1 left-1 right-1 top-1 w-[82px] rounded-md bg-white shadow transition-all duration-300"
                  style={{
                    transform:
                      inputMode === "keyboard"
                        ? "translateX(0%)"
                        : "translateX(110%)",
                  }}
                />
                {/* 键盘 tab */}
                <div
                  className={cn(
                    "relative z-10 flex h-8 w-1/2 cursor-pointer items-center justify-center rounded-md transition-colors duration-300",
                    inputMode === "keyboard"
                      ? "font-bold text-[rgba(51,48,45,0.85)]"
                      : "text-[rgba(51,48,45,0.55)]"
                  )}
                  onClick={() => handleModeChange("keyboard")}
                >
                  <Keyboard className="mr-1 h-4 w-4" />
                  <span className="text-xs">键盘输入</span>
                </div>
                {/* 拍照 tab */}
                <div
                  className={cn(
                    "relative z-10 flex h-8 w-1/2 cursor-pointer items-center justify-center rounded-md transition-colors duration-300",
                    inputMode === "camera"
                      ? "font-bold text-[rgba(51,48,45,0.85)]"
                      : "text-[rgba(51,48,45,0.55)]"
                  )}
                  onClick={() => handleModeChange("camera")}
                >
                  <Camera className="mr-1 h-4 w-4" />
                  <span className="text-xs">拍照作答</span>
                </div>
              </div>
            </>
          )}
        </div>
        {inputMode === "keyboard" ? (
          <KeyboardInput type={type} viewModel={fillBlankViewModel} />
        ) : (
          <>
            {!isReview && (
              <CameraInput
                imageFiles={imageFiles}
                onRemoveImage={handleRemoveImage}
                onRetryUpload={handleRetryUpload}
                onTriggerNativeUpload={() => {
                  toast.info("预览模式下不支持拍照作答");

                  // fillBlankViewModel.triggerNativeImageUpload
                }}
              />
            )}
          </>
        )}
        {/* 输入区域 */}
        <div className="flex-1">
          {isReview && (
            <>
              <div className="">
                <p className="text-[1.0625rem] font-bold text-[rgba(51,48,45,0.95)]">
                  我的答案：
                </p>
                {inputMode === "keyboard" ? (
                  <span className="text-[rgba(51,48,45,0.55)]">
                    {answers[activeBlankIndex]?.value || "（未作答）"}
                  </span>
                ) : (
                  <div className="mt-4 flex gap-2">
                    {imageFiles.length === 0 ? (
                      <span className="text-[rgba(51,48,45,0.55)]">
                        （未上传图片）
                      </span>
                    ) : (
                      imageFiles.map((img) => (
                        <img
                          key={img.id}
                          src={img.preview}
                          alt="图片答案"
                          style={{
                            maxWidth: 120,
                            maxHeight: 120,
                            borderRadius: 6,
                          }}
                        />
                      ))
                    )}
                  </div>
                )}
              </div>
              <div className="mt-4 flex">
                <span className="text-[1.0625rem] font-bold text-[rgba(51,48,45,0.95)]">
                  正确答案：
                </span>
              </div>

              <div className="mt-4 rounded-xl border border-solid border-[rgba(88,196,250,0.15)] bg-[#E3F5FF] px-6 py-4">
                <span className="text-[#476275]">
                  <FormatMath
                    questionId={question?.questionId}
                    htmlContent={correctAnswers[activeBlankIndex] || ""}
                  />
                </span>
                {/* 自评结果区 */}
                {type !== "english-fill-blank" && (
                  <div className="mt-4 flex gap-4">
                    <button
                      className={cn(
                        "flex h-8 w-[5.75rem] items-center justify-center rounded-[9px] border shadow-[0_2px_0_0_rgba(51,46,41,0.12)] transition-all",
                        selfEvaluation[activeBlankIndex] === "right"
                          ? "border-[#84D64B] bg-[#EDF9E4] text-[#449908] shadow-[0_2px_0_0_rgba(132,214,75,0.6)]"
                          : "border-[rgba(51,46,41,0.12)] bg-white text-[rgba(51,48,45,0.95)]"
                      )}
                      onClick={() => viewModel.handleSelfEvaluate("right")}
                    >
                      {selfEvaluation[activeBlankIndex] === "right" ? (
                        <RightIconGreen />
                      ) : (
                        <RightIconNormal />
                      )}
                      <span className="ml-1 text-xs">我答对了</span>
                    </button>
                    <button
                      className={cn(
                        "flex h-8 w-[5.75rem] items-center justify-center rounded-[9px] border shadow-[0_2px_0_0_rgba(51,46,41,0.12)] transition-all",
                        selfEvaluation[activeBlankIndex] === "partial"
                          ? "border-[#FF7B59] bg-[#FFEBE6] text-[#D1320A] shadow-[0_2px_0_0_rgba(255,123,89,0.6)]"
                          : "border-[rgba(51,46,41,0.12)] bg-white text-[rgba(51,48,45,0.95)]"
                      )}
                      onClick={() => viewModel.handleSelfEvaluate("partial")}
                    >
                      {selfEvaluation[activeBlankIndex] === "partial" ? (
                        <HalfRightIconRed />
                      ) : (
                        <HalfRightIconNormal />
                      )}
                      <span className="ml-1 text-xs">部分答对</span>
                    </button>
                    <button
                      className={cn(
                        "flex h-8 w-[5.75rem] items-center justify-center rounded-[9px] border shadow-[0_2px_0_0_rgba(51,46,41,0.12)] transition-all",
                        selfEvaluation[activeBlankIndex] === "wrong"
                          ? "border-[#FF7B59] bg-[#FFEBE6] text-[#D1320A] shadow-[0_2px_0_0_rgba(255,123,89,0.6)]"
                          : "border-[rgba(51,46,41,0.12)] bg-white text-[rgba(51,48,45,0.95)]"
                      )}
                      onClick={() => viewModel.handleSelfEvaluate("wrong")}
                    >
                      {selfEvaluation[activeBlankIndex] === "wrong" ? (
                        <WrongIconRed />
                      ) : (
                        <WrongIconNormal />
                      )}
                      <span className="ml-1 text-xs">我答错了</span>
                    </button>
                  </div>
                )}
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};
