import { QUESTION_TYPE } from "@repo/core/enums";
import { upload } from "@repo/lib/utils/oss";
import { RefObject, useCallback, useEffect, useRef, useState } from "react";
import { NextQuestionInfo } from "../../model";
import { useQuestionPreviewContext } from "../contexts/question-preview-context";
import { parseContent } from "../../utils/question/parseFillBlank";

export interface BlankAnswer {
  id: number;
  value: string;
}

export interface FillBlankViewModel {
  isReview: boolean;
  question: NextQuestionInfo;
  answers: BlankAnswer[];
  activeBlankIndex: number;
  inputMode: InputMode;
  imageFiles: ImageFile[];
  setAnswers: (answers: BlankAnswer[]) => void;
  fileInputRef: RefObject<HTMLInputElement | null>;
  setActiveBlankIndex: (index: number) => void;
  setInputMode: (mode: InputMode) => void;
  handleTextChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  handleModeChange: (mode: InputMode) => void;
  handleRemoveImage: (id: number) => void;
  handleSubmitWithDialog: () => void;
  handleRetryUpload: (id: number) => void;
  correctAnswers: string[];
  selfEvaluation: ("right" | "partial" | "wrong" | null)[];
  setSelfEvaluation: React.Dispatch<
    React.SetStateAction<("right" | "partial" | "wrong" | null)[]>
  >;
  onSelfEvaluate: (type: "right" | "partial" | "wrong") => void;
  handleContinueReview: () => void;
  handleBlankClick: (index: number) => void;
  openInputDialog: (initialValue: string, index: number) => void;
  closeInputDialog: () => void;
  confirmInputDialog: () => void;
  showInputDialog: boolean;
  inputDialogValue: string;
  setInputDialogValue: (val: string) => void;
  textareaAutoResize: (ref: HTMLTextAreaElement | null) => void;
  handleSelfEvaluate: (type: "right" | "partial" | "wrong") => void;
  triggerNativeImageUpload: () => void;
  isEnglishFillBlank: boolean;
}

export interface ImageFile {
  id: number;
  file: File;
  preview: string;
  status?: "pending" | "success" | "error" | "audit-failed";
}

export type InputMode = "keyboard" | "camera";

// 添加防抖函数
function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// 添加重试函数
async function retry<T>(
  fn: () => Promise<T>,
  retries: number = 3,
  delay: number = 1000
): Promise<T> {
  try {
    return await fn();
  } catch (error) {
    if (retries === 0) throw error;
    await new Promise((resolve) => setTimeout(resolve, delay));
    return retry(fn, retries - 1, delay);
  }
}

// 添加答案比较函数
function compareAnswers(
  answer: string,
  correctAnswer: string | undefined
): "right" | "partial" | "wrong" {
  const normalizedAnswer = answer.trim().toLowerCase();
  const normalizedCorrect = correctAnswer?.trim().toLowerCase();

  if (!normalizedAnswer || !normalizedCorrect) return "wrong";

  if (normalizedAnswer === normalizedCorrect) {
    return "right";
  }

  if (
    normalizedCorrect.includes(normalizedAnswer) ||
    normalizedAnswer.includes(normalizedCorrect)
  ) {
    return "partial";
  }

  return "wrong";
}

export function useFillBlankViewModel(question: NextQuestionInfo) {
  // 获取统一的Context配置
  const { questionState, setQuestionState, updateUserAnswer, studentAnswer } =
    useQuestionPreviewContext();
  // console.log("studentAnswer", studentAnswer.answerContents[0].selfEvaluations[0]);
  const blankCount = parseContent(
    question.questionContent.questionStem || ""
  ).reduce(
    (count, paragraph) =>
      count + paragraph.filter((item) => item === null).length,
    0
  );
  const [answers, setAnswers] = useState<BlankAnswer[]>(
    Array.from({ length: blankCount }).map((_, idx) => ({
      id: idx + 1,
      value: "",
    }))
  );
  const [activeBlankIndex, setActiveBlankIndex] = useState(0);
  const [inputMode, setInputMode] = useState<InputMode>("camera");
  const [imageFiles, setImageFiles] = useState<ImageFile[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [correctAnswers, setCorrectAnswers] = useState<string[]>(
    question.questionAnswer?.answerOptionList.map(
      (option) => option.optionVal as string
    ) ?? []
  );
  // 新增：每个空的自评状态
  const [selfEvaluation, setSelfEvaluation] = useState<
    ("right" | "partial" | "wrong" | null)[]
  >(Array.from({ length: blankCount }).map(() => null));

  const [showSubmitDialog, setShowSubmitDialog] = useState(false);
  const [submitDialogType, setSubmitDialogType] = useState<
    "allEmpty" | "partEmpty" | "photoEmpty" | null
  >(null);

  const [showInputDialog, setShowInputDialog] = useState(false);
  const [inputDialogValue, setInputDialogValue] = useState("");
  const [inputDialogIndex, setInputDialogIndex] = useState<number | null>(null);

  const selfEvaluateTimestamps = useRef<number[]>([]);

  const [keyboardAnswersCache, setKeyboardAnswersCache] = useState<
    BlankAnswer[]
  >(() =>
    Array.from({ length: blankCount }).map((_, idx) => ({
      id: idx + 1,
      value: "",
    }))
  );

  const [isEnglishFillBlank] = useState(
    question.questionType === QUESTION_TYPE.QUESTION_TYPE_CLOZE
  );

  useEffect(() => {
    return () => {
      imageFiles.forEach((img) => {
        if (img.file) {
          URL.revokeObjectURL(img.preview);
        }
      });
    };
  }, [imageFiles]);

  // 监听题目变化，重置所有状态
  useEffect(() => {
    // 重新计算空白数量
    const newBlankCount = parseContent(
      question.questionContent.questionStem || ""
    ).reduce(
      (count, paragraph) =>
        count + paragraph.filter((item) => item === null).length,
      0
    );

    console.log(
      "214 questionState",
      questionState,
      question.questionType,
      studentAnswer
    );
    const firstAnswerContent = studentAnswer?.answerContents[0];
    if (
      studentAnswer &&
      studentAnswer?.answerContents?.length > 0 &&
      firstAnswerContent?.content &&
      Array.isArray(firstAnswerContent.content)
    ) {
      setAnswers(
        firstAnswerContent.content.map((item: string, index: number) => ({
          id: index + 1,
          value: item,
        }))
      );
    } else {
      setAnswers(
        Array.from({ length: newBlankCount }).map((_, idx) => ({
          id: idx + 1,
          value: "",
        }))
      );
    }
    setActiveBlankIndex(0);
    const content = studentAnswer?.answerContents[0]?.content;
    if (
      content &&
      content.length > 0 &&
      studentAnswer?.answerContents[0]?.type === 2
    ) {
      setInputMode("camera");
      setImageFiles(
        content.map((item: any) => ({
          id: 0,
          file: new File([], ""),
          preview: item,
          status: "success",
        }))
      );
    } else {
      setInputMode("keyboard");
      setImageFiles([]);
    }
    setCorrectAnswers(
      question.questionAnswer?.answerOptionList.map(
        (option) => option.optionVal as string
      ) ?? []
    );
    if (studentAnswer?.answerContents[0]?.selfEvaluations) {
      setSelfEvaluation(
        studentAnswer.answerContents[0].selfEvaluations.map((item) => {
          if (item === 1) {
            return "right";
          } else if (item === 2) {
            return "partial";
          } else if (item === 3) {
            return "wrong";
          } else {
            return "wrong";
          }
        })
      );
    } else {
      setSelfEvaluation(Array.from({ length: newBlankCount }).map(() => null));
    }
    setShowSubmitDialog(false);
    setSubmitDialogType(null);
    setShowInputDialog(false);
    setInputDialogValue("");
    setInputDialogIndex(null);
    setKeyboardAnswersCache(
      Array.from({ length: newBlankCount }).map((_, idx) => ({
        id: idx + 1,
        value: "",
      }))
    );

    // 重置时间戳
    selfEvaluateTimestamps.current = [];
  }, [question.questionId]);

  // 优化自动批改逻辑
  useEffect(() => {
    if (
      isEnglishFillBlank &&
      (questionState === "submitted" || questionState === "giving_up")
    ) {
      const newSelfEvaluation = answers.map((answer, index) => {
        const correctAnswer = correctAnswers[index];
        return compareAnswers(answer.value, correctAnswer);
      });

      setSelfEvaluation(newSelfEvaluation);
    }
  }, [isEnglishFillBlank, questionState, answers, correctAnswers]);

  function updateStatus(
    id: number,
    status: "pending" | "success" | "error" | "audit-failed"
  ) {
    setImageFiles((prev) =>
      prev.map((img) => (img.id === id ? { ...img, status } : img))
    );
  }

  // 优化图片上传函数
  async function handleImageUpload(img: ImageFile) {
    try {
      updateStatus(img.id, "pending");
      const { url, error } = await retry(() =>
        upload({
          file: img.file,
          signature: {
            url: "/api/v1/upload/token",
            params: {},
          },
        })
      );

      if (url) {
        updateStatus(img.id, "success");
        return url;
      } else {
        throw new Error(error || "上传失败");
      }
    } catch (error) {
      console.error("图片上传失败:", error);
      updateStatus(img.id, "error");
      // toast.show("图片上传失败，请重试");
      throw error;
    }
  }

  // 优化重试上传函数
  const handleRetryUpload = useCallback(
    async (id: number) => {
      const img = imageFiles.find((img) => img.id === id);
      if (!img) return;

      try {
        await handleImageUpload(img);
      } catch (error) {
        // 错误已在 handleImageUpload 中处理
      }
    },
    [imageFiles]
  );

  // 优化移除图片函数
  const handleRemoveImage = useCallback((id: number) => {
    // 预览模式下不支持移除图片
    return;
  }, []);

  function handleTextChange(e: React.ChangeEvent<HTMLTextAreaElement>) {
    // 用户主动输入内容时，如果当前是放弃作答状态，回退到答题状态
    // 预览模式下不支持输入
    return;
  }

  function handleModeChange(mode: InputMode) {
    if (mode === inputMode) return;
    if (mode === "camera") {
      // 切到拍照，缓存当前 answers 并清空 answers
      setKeyboardAnswersCache(answers);
      setAnswers(
        Array.from({ length: blankCount }).map((_, idx) => ({
          id: idx + 1,
          value: "",
        }))
      );
      setActiveBlankIndex(-1);
    } else if (mode === "keyboard") {
      setActiveBlankIndex(0);
      // 切回键盘，恢复缓存
      setAnswers(keyboardAnswersCache);
    }
    setInputMode(mode);
  }

  function handleSubmitWithDialog() {
    if (inputMode === "camera") {
      if (imageFiles.length > 0) {
        // 有图片，直接提交
        return;
      } else {
        setSubmitDialogType("photoEmpty");
        setShowSubmitDialog(true);
        return;
      }
    }
    // 文字作答
    const emptyCount = answers.filter((a) => !a.value.trim()).length;
    if (emptyCount === answers.length) {
      setSubmitDialogType("allEmpty");
      setShowSubmitDialog(true);
      return;
    } else if (emptyCount > 0) {
      setSubmitDialogType("partEmpty");
      setShowSubmitDialog(true);
      return;
    } else {
      // 全部有内容，直接提交
      return;
    }
  }

  function handleContinueSubmit() {
    setShowSubmitDialog(false);
  }

  function handleCancelSubmit() {
    setShowSubmitDialog(false);
  }

  function handleBlankClick(idx: number) {
    // 强制切换到键盘模式，即使在预览模式下也允许
    handleModeChange("keyboard");
    setActiveBlankIndex(idx);
    console.log("handleBlankClick 完成，设置 activeBlankIndex 为:", idx);
  }

  // 新增：自评按钮点击
  function onSelfEvaluate(type: "right" | "partial" | "wrong") {
    setSelfEvaluation((prev) => {
      const next = [...prev];
      next[activeBlankIndex] = type;
      return next;
    });
  }

  // 优化自评处理函数
  const handleSelfEvaluate = useCallback(
    debounce(async (type: "right" | "partial" | "wrong") => {
      return;
    }, 300),
    [
      question.questionId,
      selfEvaluation,
      inputMode,
      activeBlankIndex,
      onSelfEvaluate,
    ]
  );

  // 解析状态下"继续"按钮，跳到下一个空
  function handleContinueReview() {
    setActiveBlankIndex((prev) => {
      if (prev < answers.length - 1) {
        return prev + 1;
      }
      return prev;
    });
  }

  function openInputDialog(initialValue: string, index: number) {
    setInputDialogValue(initialValue);
    setInputDialogIndex(index);
    setShowInputDialog(true);
  }

  function closeInputDialog() {
    setShowInputDialog(false);
    // setInputDialogIndex(null);
  }

  function confirmInputDialog() {
    // 用户确认输入内容时，如果当前是放弃作答状态，回退到答题状态
    if (questionState === "giving_up" || questionState === "uncertain") {
      setQuestionState("answering");
    }

    if (inputDialogIndex !== null) {
      setAnswers((prev) => {
        const next: any = [...prev];
        next[inputDialogIndex] = {
          ...next[inputDialogIndex],
          value: inputDialogValue,
        };

        // 🔧 修复：同步到Context中的答案状态
        const subjectiveAnswer = next.map(
          (answer: BlankAnswer) => answer.value
        );
        updateUserAnswer({ subjectiveAnswer });

        return next;
      });
    }
    setShowInputDialog(false);
    setInputDialogIndex(null);
  }

  function textareaAutoResize(ref: HTMLTextAreaElement | null) {
    if (!ref) return;
    const maxRows = 3;
    const lineHeight = 32;
    const maxHeight = maxRows * lineHeight;
    ref.style.height = "auto";
    const newHeight = Math.min(ref.scrollHeight, maxHeight);
    ref.style.height = `${newHeight}px`;
  }

  async function triggerNativeImageUpload() {
    return;
  }

  return {
    question,
    answers,
    setAnswers,
    activeBlankIndex,
    setActiveBlankIndex,
    inputMode,
    setInputMode,
    imageFiles,
    setImageFiles,
    fileInputRef,
    handleTextChange,
    handleRemoveImage,
    handleModeChange,
    handleSubmitWithDialog,
    handleBlankClick,
    handleRetryUpload,
    correctAnswers,
    setCorrectAnswers,
    selfEvaluation,
    setSelfEvaluation,
    onSelfEvaluate,
    handleContinueReview,
    showSubmitDialog,
    submitDialogType,
    handleContinueSubmit,
    handleCancelSubmit,
    showInputDialog,
    inputDialogValue,
    setInputDialogValue,
    openInputDialog,
    closeInputDialog,
    confirmInputDialog,
    inputDialogIndex,
    textareaAutoResize,
    handleSelfEvaluate,
    triggerNativeImageUpload,
    isEnglishFillBlank,
  };
}
