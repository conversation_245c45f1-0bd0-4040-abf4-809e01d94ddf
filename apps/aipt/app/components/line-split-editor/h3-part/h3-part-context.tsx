import {
  calculateCharacterCount,
  getDefaultStyleByCharCount,
} from "@/app/utils/character-count";
import {
  analyzeTextLayoutForH3Part,
  type TextLayoutResult,
} from "@/app/utils/text-layout-analysis";
import { Picture } from "@repo/core/types/data/base";
import { Line } from "@repo/core/types/data/widget-guide";
import { createContext, useCallback, useContext, useMemo } from "react";
import { v4 as uuidv4 } from "uuid";
import { useLineSplitEditorContext } from "../line-split-editor-context";

interface H3PartContextType {
  index: number;
  blockIndex: number; // H3块索引，从1开始
  lines: Line[];
  addBlock: (selectedIndexList: Set<number>) => void;
  deleteBlock: (id: string) => void;
  setBlockWidth: (id: string, width: string) => void;
  setBlockStyle: (
    id: string,
    style: {
      layout: "vertical" | "horizontal";
      textRatio: number;
      imageRatio: "16:9" | "1:1" | "9:16";
      styleType: "style1" | "style2" | "style3" | "style4" | "style5";
    }
  ) => void;
  setBlockPicture: (id: string, picture: Picture) => void;
  delBlockPicture: (id: string) => void;
  // 文字排版相关
  textLayoutAnalysis: TextLayoutResult;
  hasColumnLayout: boolean;
  hasTextLayout: boolean;
  applyTextLayout: () => void;
  removeTextLayout: () => void;
}

const H3PartContext = createContext<H3PartContextType>({} as H3PartContextType);

const useH3PartContext = () => useContext(H3PartContext);

const H3PartProvider = ({
  children,
  index,
  data,
}: {
  children: React.ReactNode;
  index: number;
  data: Line[];
}) => {
  const { updatePart, setManualRemoveColumn } = useLineSplitEditorContext();

  // 简化的数据清理：只清理明显错误的OL行样式3标记
  const cleanDataForTextLayout = useCallback((lines: Line[]): Line[] => {
    return lines.map((line) => {
      // OL行不应该有样式3标记
      if (line.tag === "ol" && line.styleText === 3) {
        return { ...line, styleText: undefined };
      }
      return line;
    });
  }, []);

  // const [lines, setLines] = useState<Line[]>(data);

  const addBlock = useCallback(
    (selectedIndexList: Set<number>) => {
      const result: Line[] = [];

      const pareLinesToBlock = (lines: Line[]) => {
        if (!lines || lines.length === 0) {
          throw new Error("Cannot create block from empty lines array");
        }

        const firstLine = lines[0];
        const lastLine = lines[lines.length - 1];

        if (!firstLine || !lastLine) {
          throw new Error("Invalid lines data for block creation");
        }

        // 智能推荐：根据字符数推荐合适的样式
        const charCount = calculateCharacterCount(lines);
        const recommendedStyle = getDefaultStyleByCharCount(charCount);

        // 样式配置映射
        const styleConfig = {
          style1: {
            layout: "vertical" as const,
            imageRatio: "16:9" as const,
            textRatio: 1,
            styleType: "style1" as const,
          },
          style2: {
            layout: "horizontal" as const,
            imageRatio: "1:1" as const,
            textRatio: 0.75,
            styleType: "style2" as const,
          },
          style3: {
            layout: "horizontal" as const,
            imageRatio: "1:1" as const,
            textRatio: 0.65,
            styleType: "style3" as const,
          },
          style4: {
            layout: "horizontal" as const,
            imageRatio: "9:16" as const,
            textRatio: 0.55,
            styleType: "style4" as const,
          },
          style5: {
            layout: "vertical" as const,
            imageRatio: "16:9" as const,
            textRatio: 1,
            styleType: "style5" as const,
          },
        };

        const config =
          styleConfig[recommendedStyle as keyof typeof styleConfig] ||
          styleConfig.style2;

        // 保留原始行的图片，但将第一个有图片的行的图片提升到block层级
        const firstPicLine = lines.find((line) => line.pic);
        const cleanedLines = lines.map((line) => ({
          ...line,
          pic: undefined, // 清除原始行的图片，图片将在block层级管理
        }));

        const block: Line = {
          id: uuidv4(),
          tag: "block",
          level: firstLine?.level ?? 0,
          inFrame: firstLine?.inFrame ?? 0,
          outFrame: lastLine?.outFrame ?? 0,
          width: `${config.textRatio * 100}%`,
          layout: config.layout,
          imageRatio: config.imageRatio,
          styleType: config.styleType, // 记录推荐的样式类型
          pic: firstPicLine?.pic, // 将第一个有图片的行的图片提升到block层级
          content: cleanedLines, // 使用清理后的行
        };

        return block;
      };
      // 修复：正确处理分栏位置，确保block出现在原始行的位置
      let i = 0;
      while (i < data.length) {
        if (selectedIndexList.has(i)) {
          // 找到连续选中的行
          const group: Line[] = [];
          while (i < data.length && selectedIndexList.has(i)) {
            const line = data[i];
            if (line && typeof line === "object") {
              group.push(line);
            }
            i++;
          }
          // 将这些行合并为一个block
          if (group.length > 0) {
            result.push(pareLinesToBlock(group));
          }
        } else {
          // 未选中的行直接添加
          const line = data[i];
          if (line && typeof line === "object") {
            result.push(line);
          }
          i++;
        }
      }

      // 更新part
      updatePart(index, result);
    },
    [data, index, updatePart]
  );

  const deleteBlock = useCallback(
    (id: string) => {
      // 找到index对应的target
      const target = data.find((line) => line.id === id);

      if (!target || target.tag !== "block") {
        return; // 如果不是block或者不存在，直接返回
      }

      // 创建新的数组，将index位置的block替换为其content
      const newLines: Line[] = [];

      data.forEach((line) => {
        if (line.id === id) {
          // 如果是要删除的block，将其content展开添加到结果中
          if (line.content && Array.isArray(line.content)) {
            // 检查content是否为Line数组（block类型的content）
            const contentLines = line.content as Line[];
            newLines.push(...contentLines);
          }
        } else {
          // 保留其他行
          newLines.push(line);
        }
      });
      //更新part
      updatePart(index, newLines);
      setManualRemoveColumn(true); // 新增
    },
    [data, index, updatePart, setManualRemoveColumn]
  );

  const setBlockWidth = useCallback(
    (id: string, width: string) => {
      // 找到index对应的target
      const target = data.find((line) => line.id === id);

      if (!target || target.tag !== "block") {
        return; // 如果不是block或者不存在，直接返回
      }

      // 创建新的数组，将index位置的block替换为修改后的block
      const newLines: Line[] = [];

      data.forEach((line) => {
        if (line.id === id) {
          line.width = width;
        }
        newLines.push(line);
      });
      //更新part
      updatePart(index, newLines);
    },
    [data, index, updatePart]
  );

  const setBlockStyle = useCallback(
    (
      id: string,
      style: {
        layout: "vertical" | "horizontal";
        textRatio: number;
        imageRatio: "16:9" | "1:1" | "9:16";
        styleType: "style1" | "style2" | "style3" | "style4" | "style5";
      }
    ) => {
      const newLines = data.map((line) => {
        if (line.id === id && line.tag === "block") {
          const updatedLine = {
            ...line,
            layout: style.layout,
            imageRatio: style.imageRatio,
            width: `${style.textRatio * 100}%`,
            styleType: style.styleType, // 记录用户选择的真实样式类型
          };
          return updatedLine;
        }
        return line;
      });

      updatePart(index, newLines);

      // 修复：样式改变后强制重新计算AI圈画位置
      // 确保DOM更新完成后再触发重新计算
      setTimeout(() => {
        // 触发所有 RoughNotion 组件重新计算位置
        const roughNotionElements = document.querySelectorAll(
          "[data-rough-notation]"
        );
        roughNotionElements.forEach((element) => {
          // 触发重新渲染
          const event = new Event("resize", { bubbles: true });
          element.dispatchEvent(event);
        });
      }, 100);
    },
    [data, index, updatePart]
  );

  const setBlockPicture = useCallback(
    (id: string, picture: Picture) => {
      // 找到index对应的target
      const target = data.find((line) => line.id === id);

      if (!target || target.tag !== "block") {
        return; // 如果不是block或者不存在，直接返回
      }

      // 创建新的数组，将index位置的block替换为修改后的block
      const newLines: Line[] = [];

      data.forEach((line) => {
        if (line.id === id) {
          line.pic = picture;
        }
        newLines.push(line);
      });
      //更新part
      updatePart(index, newLines);
    },
    [data, index, updatePart]
  );

  const delBlockPicture = useCallback(
    (id: string) => {
      // 找到index对应的target
      const target = data.find((line) => line.id === id);

      if (!target || target.tag !== "block") {
        return; // 如果不是block或者不存在，直接返回
      }

      // 创建新的数组，将index位置的block替换为修改后的block
      const newLines: Line[] = [];

      data.forEach((line) => {
        if (line.id === id) {
          line.pic = undefined;
        }
        newLines.push(line);
      });
      //更新part
      updatePart(index, newLines);
    },
    [data, index, updatePart]
  );

  // 文字排版分析
  const textLayoutAnalysis = useMemo(() => {
    const cleanedData = cleanDataForTextLayout(data);
    const analysis = analyzeTextLayoutForH3Part(cleanedData);
    return analysis;
  }, [data, cleanDataForTextLayout]);

  // 检查是否已有分栏
  const hasColumnLayout = useMemo(() => {
    // 只有包含layout属性的block才是真正的分栏布局
    return data.some(
      (line: Line) =>
        line.tag === "block" &&
        line.layout &&
        (line.layout === "horizontal" || line.layout === "vertical")
    );
  }, [data]);

  // 简化的文字排版检查
  const hasTextLayout = useMemo(() => {
    return data.some(
      (line) =>
        // 样式1、2：OL行有styleText标记
        (line.tag === "ol" && line.styleText && line.styleText > 0) ||
        // 样式3：block容器有styleText=3标记
        (line.tag === "block" && line.styleText === 3)
    );
  }, [data]);

  // 🔧 辅助函数：找到包含指定OL行的父级容器索引
  const findParentContainerIndex = (lines: Line[], olIndex: number): number => {
    // 向前查找最近的h3、h4或block容器
    for (let i = olIndex - 1; i >= 0; i--) {
      const line = lines[i];
      if (
        line &&
        (line.tag === "h3" || line.tag === "h4" || line.tag === "block")
      ) {
        return i;
      }
    }
    return -1;
  };

  // 简化的应用文字排版
  const applyTextLayout = useCallback(() => {
    if (!textLayoutAnalysis.canUseTextLayout) return;

    const applicableGroups = textLayoutAnalysis.groupResults.filter(
      (result) => result.canUseStyle
    );
    if (applicableGroups.length === 0) return;

    const cleanedData = cleanDataForTextLayout(data);
    let newLines: Line[] = [...cleanedData];

    // 清理旧的样式3容器
    newLines = newLines.filter(
      (line) => !(line.tag === "block" && line.styleText === 3)
    );

    applicableGroups.forEach((group) => {
      if (!group.olLines || group.olLines.length === 0) return;

      if (group.styleToApply === 1 || group.styleToApply === 2) {
        // 样式1、2：给OL行和其父级容器都添加styleText
        group.olLines.forEach((olLine) => {
          const lineIndex = newLines.findIndex((line) => line.id === olLine.id);
          if (lineIndex !== -1) {
            newLines[lineIndex] = { ...olLine, styleText: group.styleToApply };

            // 🔧 新增：给父级容器也设置styleText
            // 找到包含这个OL的父级容器（h3、h4或block）
            const parentIndex = findParentContainerIndex(newLines, lineIndex);
            if (parentIndex !== -1 && newLines[parentIndex]) {
              const parentLine = newLines[parentIndex]!;
              newLines[parentIndex] = {
                ...parentLine,
                styleText: group.styleToApply,
              };
            }
          }
        });
      } else if (group.styleToApply === 3) {
        // 样式3：创建包装容器
        const validOlItems = group.olLines
          .map((ol) => ({
            idx: newLines.findIndex((line) => line.id === ol.id),
            ol,
          }))
          .filter((item) => item.idx !== -1);

        if (validOlItems.length === 0) return;

        const firstIndex = validOlItems[0]!.idx;
        const wrapperId = `text-layout-style3-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

        // 创建包装容器
        const wrappedOLs = validOlItems.map((item) => ({
          ...JSON.parse(JSON.stringify(item.ol)),
          styleText: undefined,
        }));

        const wrapperLine: Line = {
          id: wrapperId,
          tag: "block",
          level: newLines[firstIndex]?.level || 0,
          inFrame: newLines[firstIndex]?.inFrame || 0,
          outFrame: newLines[firstIndex]?.outFrame || 0,
          content: wrappedOLs,
          styleText: 3,
        };

        // 替换第一个OL行为包装容器，删除其余OL行
        newLines[firstIndex] = wrapperLine;
        validOlItems
          .slice(1)
          .map((item) => item.idx)
          .sort((a, b) => b - a)
          .forEach((idx) => newLines.splice(idx, 1));
      }
    });

    updatePart(index, newLines);
  }, [textLayoutAnalysis, data, cleanDataForTextLayout, index, updatePart]);

  // 简化的移除文字排版
  const removeTextLayout = useCallback(() => {
    const newLines: Line[] = [];

    data.forEach((line: Line) => {
      if (line.tag === "ol" && line.styleText && line.styleText > 0) {
        // 移除样式1、2：清除OL行的styleText
        newLines.push({ ...line, styleText: undefined });
      } else if (
        line.tag === "block" &&
        line.styleText === 3 &&
        Array.isArray(line.content)
      ) {
        // 移除样式3：拆解包装容器，恢复OL行
        (line.content as Line[]).forEach((olLine) => {
          newLines.push({ ...olLine, styleText: undefined });
        });
      } else if (
        (line.tag === "h3" || line.tag === "h4" || line.tag === "block") &&
        line.styleText &&
        (line.styleText === 1 || line.styleText === 2)
      ) {
        // 🔧 新增：移除父级容器的styleText（样式1、2）
        newLines.push({ ...line, styleText: undefined });
      } else {
        // 保留其他行
        newLines.push(line);
      }
    });

    updatePart(index, newLines);
    setManualRemoveColumn(true); // 用户手动删除排版，阻止自动排版
  }, [data, index, updatePart, setManualRemoveColumn]);

  const value = {
    index,
    blockIndex: index + 1, // H3块索引从1开始，所以是 index + 1
    lines: data,
    addBlock,
    deleteBlock,
    setBlockWidth,
    setBlockStyle,
    setBlockPicture,
    delBlockPicture,
    textLayoutAnalysis,
    hasColumnLayout,
    hasTextLayout,
    applyTextLayout,
    removeTextLayout,
  };
  return <H3PartContext value={value}>{children}</H3PartContext>;
};

export { H3PartProvider, useH3PartContext };
