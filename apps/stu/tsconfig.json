{"extends": "@repo/config-typescript/next-js.json", "compilerOptions": {"paths": {"@/*": ["./*"], "@repo/ui/*": ["../../packages/ui/src/*"], "@repo/core/*": ["../../packages/core/src/*"], "@repo/lib/*": ["../../packages/lib/src/*"]}, "plugins": [{"name": "next"}], "target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true}, "include": ["**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "next-env.d.ts", "next.config.ts", "dist/types/**/*.ts", "../../packages/core/src/course/course-summary.tsx", "../../packages/core/src/exercise/components/StuBackButton.tsx", "../../packages/core/src/components/iframe-preview/index.tsx"], "exclude": ["node_modules"]}