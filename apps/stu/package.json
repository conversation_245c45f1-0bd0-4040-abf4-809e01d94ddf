{"name": "stu", "version": "0.0.10", "private": true, "scripts": {"build": "next build", "build:dev": "NODE_ENV=dev next build", "build:test": "NODE_ENV=test next build && node scripts/static-upload.js", "build:prod": "NODE_ENV=production next build && node scripts/static-upload.js", "build:analyzer": "NEXT_PUBLIC_ANALYZE=true NODE_ENV=production next build", "dev": "next dev --turbo --port=3020", "start": "next start --port=3020", "lint": "next lint && tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "test:models": "repo-test-models -F=stu", "test:models:coverage": "repo-test-models -F=stu --coverage", "test:model": "repo-test-models -F=stu", "test:api": "VITEST_API=true vitest run", "test:api:coverage": "VITEST_API=true vitest run --coverage", "test:api:watch": "VITEST_API=true vitest --watch", "remotion": "remotion studio", "render": "remotion render", "deploy:oss": "next build && node deploy_oss.js", "pm:test": "next dist/standalone/server.js", "zip": "node scripts/zip-build.js", "build:local": "next build && node scripts/static-upload.js", "deploy:dev": "NODE_ENV=local next build && { pm2 del schroolroom-stu || true; pm2 start -n schroolroom-stu pnpm -- run start; }"}, "dependencies": {"@emotion/css": "^11.13.5", "@next/bundle-analyzer": "^15.4.4", "@preact-signals/safe-react": "^0.9.0", "@remotion/animation-utils": "catalog:remotion4", "@remotion/cli": "catalog:remotion4", "@remotion/media-utils": "catalog:remotion4", "@remotion/player": "catalog:remotion4", "@remotion/preload": "catalog:remotion4", "@remotion/tailwind-v4": "catalog:remotion4", "@remotion/zod-types": "catalog:remotion4", "@repo/core": "workspace:*", "@repo/lib": "workspace:*", "@repo/rough-notation": "workspace:*", "@repo/ui": "workspace:*", "@sentry/nextjs": "^9.43.0", "@sentry/profiling-node": "^9.43.0", "@use-gesture/react": "^10.3.1", "better-react-mathjax": "^2.3.0", "clsx": "^2.1.1", "framer-motion": "^12.4.2", "immer": "^10.1.1", "katex": "^0.16.21", "lint-staged": "^15.4.3", "lottie-react": "^2.4.1", "lucide-react": "^0.475.0", "mathlive": "^0.105.3", "motion": "^12.4.7", "next": "15.3.2", "next-themes": "^0.4.4", "react": "catalog:react19", "react-dom": "catalog:react19", "remotion": "catalog:remotion4", "socket.io-client": "^4.8.1", "swr": "^2.3.2", "tailwind-merge": "^3.0.1", "use-debounce": "^10.0.4", "use-double-tap": "^1.3.7", "use-long-press": "^3.3.0", "uuid": "^11.1.0", "zod": "3.22.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@repo/config-eslint": "workspace:*", "@repo/config-typescript": "workspace:*", "@svgr/webpack": "^8.1.0", "@tailwindcss/postcss": "^4.1.3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.1.2", "@types/node": "^20", "@types/react": "catalog:react19", "@types/react-dom": "catalog:react19", "ali-oss": "^6.22.0", "archiver": "^6.0.2", "eslint-config-next": "15.1.7", "eslint-config-prettier": "^10.1.1", "husky": "^9.1.7", "jsdom": "^23.0.1", "postcss": "^8.5.3", "tailwindcss": "^4.1.3", "tw-animate-css": "^1.2.5", "typescript": "5.8.3", "vconsole": "^3.15.1", "vitest": "^1.1.0"}}